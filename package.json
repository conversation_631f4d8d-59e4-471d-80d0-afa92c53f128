{"name": "funblocks-prompt-optimizer", "version": "1.0.1", "description": "It helps you get better answers from AI tools like ChatGPT by optimizing your prompts with one-click solutions, dynamic forms for missing information, and related questions/topics exploration. It improves accuracy, saves time, and enhances your prompting skills across multiple AI platforms.", "main": "src/background.js", "scripts": {"build": "webpack --config webpack.config.js", "dev": "webpack --config webpack.config.js --mode=development --watch", "package": "npm run build && zip -r ai-prompt-optimizer.zip dist", "clean": "rm -rf dist node_modules"}, "keywords": ["chrome-extension", "ai", "prompt", "optimizer", "chatgpt", "gemini", "claude", "deepseek"], "author": "", "license": "MIT", "dependencies": {"@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "react": "^19.1.0", "react-dom": "^19.1.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}}