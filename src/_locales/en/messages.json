{"appName": {"message": "AI Prompt Optimizer - Refine your questions and prompts", "description": "The name of the extension"}, "appDesc": {"message": "Refine prompts for better AI response. Generate related questions & topics. Works with ChatGPT, <PERSON>, <PERSON>, Perplexity, DeepSeek", "description": "The description of the extension"}, "optimizeButtonText": {"message": "Refine Prompt", "description": "Text for the optimize button"}, "optimizingText": {"message": "Refining...", "description": "Text shown while optimizing"}, "settingsTitle": {"message": "Settings", "description": "Title for the settings section"}, "languageLabel": {"message": "Language", "description": "Label for language selection"}, "apiUrlLabel": {"message": "API URL", "description": "Label for API URL input"}, "saveButton": {"message": "Save", "description": "Text for save button"}, "errorMessage": {"message": "An error occurred. Please try again.", "description": "Error message"}, "successMessage": {"message": "Operate successfully!", "description": "Success message"}, "optimizedPromptTitle": {"message": "Refined Prompt", "description": "Title for the optimized prompt popup"}, "requirementsLabel": {"message": "Analysis:", "description": "Label for the requirements/analysis section in the popup"}, "promptLabel": {"message": "Refined Prompt:", "description": "Label for the optimized prompt section in the popup"}, "confirmButtonText": {"message": "Confirm", "description": "Text for the confirm button in the popup"}, "cancelButtonText": {"message": "Cancel", "description": "Text for the cancel button in the popup"}, "relatedQuestionsTopicsButtonText": {"message": "Related Questions & Topics", "description": "Text for the related questions and topics button"}, "relatedQuestionsTopicsTitle": {"message": "Related Questions & Topics", "description": "Title for the related questions and topics popup"}, "relatedQuestionsLabel": {"message": "Related Questions:", "description": "Label for the related questions section in the popup"}, "relatedTopicsLabel": {"message": "Related Topics:", "description": "Label for the related topics section in the popup"}, "loadingText": {"message": "Loading...", "description": "Text shown while loading content"}, "errorTitle": {"message": "Error", "description": "Title for error popup"}, "closeButtonText": {"message": "Close", "description": "Text for close button in error popup"}, "errorServiceUnavailable": {"message": "Service is currently unavailable. Please try again later.", "description": "Error message when service is unavailable"}, "errorLoginRequired": {"message": "Login is required to use this feature.", "description": "Error message when login is required"}, "errorExceedFreeTrialQuota": {"message": "You have exceeded your free trial quota.", "description": "Error message when free trial quota is exceeded"}, "errorExceedDailyQuota": {"message": "You have exceeded your daily quota.", "description": "Error message when daily quota is exceeded"}, "errorExceedMsgLimit": {"message": "You have exceeded the message limit.", "description": "Error message when message limit is exceeded"}, "errorRelatedQuestionsTopics": {"message": "Failed to get related questions and topics. Please try again later.", "description": "Error message when failed to get related questions and topics"}, "errorAIGenerationFailed": {"message": "AI generation failed. Please try again.", "description": "Error message when AI generation fails or returns invalid data"}, "errorGeneralError": {"message": "An error occurred. Please try again.", "description": "General error message for unknown errors"}, "notLoggedIn": {"message": "Not logged in", "description": "Text shown when user is not logged in"}, "loginButton": {"message": "<PERSON><PERSON>", "description": "Text for login button"}, "pricingButton": {"message": "Upgrade AI Plan", "description": "Text for upgrade AI plan button"}, "optionsButton": {"message": "Settings", "description": "Text for options button"}, "optionsTitle": {"message": "AI Prompt Optimizer - Settings", "description": "Title for the options page"}, "settingsSaved": {"message": "Setting<PERSON> saved successfully!", "description": "Message shown when settings are saved"}, "welcomeMessage": {"message": "Welcome to AI Prompt Optimizer", "description": "Welcome message on the options page"}, "accountSection": {"message": "Account", "description": "Title for account section on options page"}, "preferencesSection": {"message": "Preferences", "description": "Title for preferences section on options page"}, "advancedSection": {"message": "Beyond Prompt Engineering", "description": "Title for advanced section on options page"}, "beyondPromptEngineering": {"message": "Beyond Prompt Engineering", "description": "Title for the beyond prompt engineering card"}, "keyInsight": {"message": "Key Insight", "description": "Label for key insight badge"}, "criticalThinkingMessage": {"message": "While Prompt Engineering is important, Critical Thinking is the key to unlocking AI's full potential.", "description": "Main message about critical thinking"}, "askBetterQuestions": {"message": "Ask better questions", "description": "Feature item for asking better questions"}, "discoverNewPerspectives": {"message": "Discover new perspectives", "description": "Feature item for discovering new perspectives"}, "generateRelatedTopics": {"message": "Generate related topics", "description": "Feature item for generating related topics"}, "introducingAIFlow": {"message": "Introducing AIFlow", "description": "Title for the AIFlow introduction card"}, "featuredProduct": {"message": "Featured Product", "description": "Label for featured product badge"}, "aiflowMessage": {"message": "Experience the next evolution in AI interaction with Infinite Canvas + Mindmap", "description": "Main message about AIFlow"}, "enhancedThinking": {"message": "Enhanced Thinking", "description": "Title for enhanced thinking features"}, "criticalThinking": {"message": "Critical Thinking", "description": "Feature item for critical thinking"}, "creativeThinking": {"message": "Creative Thinking", "description": "Feature item for creative thinking"}, "visualOrganization": {"message": "Visual Organization", "description": "Feature item for visual organization"}, "keyBenefits": {"message": "Key Benefits", "description": "Title for key benefits features"}, "infiniteCanvas": {"message": "Infinite Canvas", "description": "Feature item for infinite canvas"}, "mindMapping": {"message": "Mind Mapping", "description": "Feature item for mind mapping"}, "aiPoweredExploration": {"message": "AI-Powered Interactive Exploration", "description": "Feature item for AI-powered exploration"}, "questionRefinement": {"message": "Question Refinement", "description": "Feature item for question refinement"}, "topicExploration": {"message": "Topic Exploration", "description": "Feature item for topic exploration"}, "tryAIFlowNow": {"message": "Try AIFlow Now", "description": "Text for the AIFlow CTA button"}, "openAIFlowButton": {"message": "Open FunBlocks AIFlow", "description": "Text for the button to open FunBlocks AIFlow"}, "openAIFlowTooltip": {"message": "FunBlocks AIFlow provides better communication with AI", "description": "Tooltip text for the FunBlocks AIFlow button"}, "loginFunBlocksButton": {"message": "<PERSON><PERSON>", "description": "Text for the button to login to FunBlocks AI account"}, "currentPlanLabel": {"message": "Current Plan:", "description": "Label for the current service plan"}, "upgradePlanButton": {"message": "Upgrade Plan", "description": "Text for the button to upgrade service plan"}, "quotaHeaderLabel": {"message": "AI-related quotas (Remaining/Total):", "description": "Header text for the quota information section"}, "unlimitedLabel": {"message": "Unlimited", "description": "Text shown for unlimited quota"}, "refreshQuotaButton": {"message": "Refresh Quota Information", "description": "Text for the button to refresh quota information"}, "quotaSectionTitle": {"message": "Service Plan & Quota", "description": "Title for the service plan and quota section"}, "noQuotaDataMessage": {"message": "No quota data available.", "description": "Message shown when no quota data is available"}, "tellMeMoreAbout": {"message": "Tell me more about:", "description": "Prefix added before related topics when inserted into input field"}, "betterQuestionText": {"message": "Better Questions", "description": "Text for the better questions menu item"}, "optimizeInstructionText": {"message": "Optimize Instruction", "description": "Text for the optimize instructions menu item"}, "optimizeImagePromptText": {"message": "Optimize Image Generation Prompt", "description": "Text for the optimize instructions menu item"}, "optimizedQuestionsTitle": {"message": "Refine Question", "description": "Title for the optimized questions popup"}, "promptDetailFormTitle": {"message": "Optimize Instructions", "description": "Title for the prompt detail form popup"}, "fundamentalRequirementsLabel": {"message": "Basic Requirements:", "description": "Label for the fundamental requirements section in the form popup"}, "reasoningForMoreInfoLabel": {"message": "More Information Needed:", "description": "Label for the reasoning for more info section in the form popup"}, "additionalInfoLabel": {"message": "Please provide more information:", "description": "Label for the additional information form section"}, "optimizedPromptLabel": {"message": "Optimized Prompt:", "description": "Label for the optimized prompt section in the form popup"}, "additionalInfoPrefix": {"message": "[additional info or requirements]:", "description": "Prefix for additional information in the final prompt"}, "otherInputPlaceholder": {"message": "Please specify...", "description": "Placeholder text for the 'Other' input field"}}