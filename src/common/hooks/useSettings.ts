import { useState, useEffect } from 'react';

interface Settings {
  apiUrl: string;
  language: string;
}

export const useSettings = () => {
  const [settings, setSettings] = useState<Settings>({
    apiUrl: '',
    language: 'en'
  });
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<{ type: 'success' | 'error' | null; message: string }>({
    type: null,
    message: ''
  });

  useEffect(() => {
    // Load settings when component mounts
    loadSettings();
  }, []);

  const loadSettings = () => {
    setLoading(true);
    chrome.runtime.sendMessage({ action: 'getSettings' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error loading settings:', chrome.runtime.lastError);
        setStatus({
          type: 'error',
          message: 'Failed to load settings'
        });
      } else {
        setSettings({
          apiUrl: response.apiUrl || '',
          language: response.language || 'en'
        });
      }
      setLoading(false);
    });
  };

  const saveSettings = () => {
    return new Promise<boolean>((resolve) => {
      chrome.runtime.sendMessage({
        action: 'saveSettings',
        settings
      }, (response) => {
        if (chrome.runtime.lastError || !response || !response.success) {
          console.error('Error saving settings:', chrome.runtime.lastError || 'Unknown error');
          setStatus({
            type: 'error',
            message: chrome.i18n.getMessage('errorMessage')
          });
          resolve(false);
        } else {
          setStatus({
            type: 'success',
            message: chrome.i18n.getMessage('successMessage')
          });

          // Clear status after 3 seconds
          setTimeout(() => {
            setStatus({ type: null, message: '' });
          }, 3000);

          resolve(true);
        }
      });
    });
  };

  const updateSetting = (key: keyof Settings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return {
    settings,
    loading,
    status,
    saveSettings,
    updateSetting
  };
};

export default useSettings;
