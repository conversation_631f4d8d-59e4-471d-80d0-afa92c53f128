// Localization manager for handling multiple languages

// Define interfaces for message structure
interface MessageItem {
  message: string;
  description?: string;
}

interface MessagesCollection {
  [key: string]: MessageItem;
}

// Singleton class to manage localization
class LocalizationManager {
  private static instance: LocalizationManager;
  private messages: { [locale: string]: MessagesCollection } = {};
  private currentLocale: string = 'en';
  private defaultLocale: string = 'en';
  private initialized: boolean = false;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): LocalizationManager {
    if (!LocalizationManager.instance) {
      LocalizationManager.instance = new LocalizationManager();
    }
    return LocalizationManager.instance;
  }

  // Initialize the localization manager
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Get current language from storage
      const result = await new Promise<{ language?: string }>((resolve) => {
        chrome.storage.local.get(['language'], resolve);
      });

      this.currentLocale = result.language || this.defaultLocale;
      
      // Load messages for current locale
      await this.loadMessages(this.currentLocale);
      
      // Also load default locale messages as fallback
      if (this.currentLocale !== this.defaultLocale) {
        await this.loadMessages(this.defaultLocale);
      }

      this.initialized = true;
    } catch (error) {
      console.error('Error initializing localization manager:', error);
      // Load default locale as fallback
      await this.loadMessages(this.defaultLocale);
      this.initialized = true;
    }
  }

  // Load messages for a specific locale
  private async loadMessages(locale: string): Promise<void> {
    try {
      // First try to use Chrome's built-in i18n API
      const appName = chrome.i18n.getMessage('appName');
      if (appName && locale === chrome.i18n.getUILanguage()) {
        // Chrome's i18n API is working and matches our locale, no need to load manually
        return;
      }
      
      // If Chrome's i18n API doesn't work or doesn't match our locale, load manually
      const response = await fetch(chrome.runtime.getURL(`_locales/${locale}/messages.json`));
      if (!response.ok) {
        throw new Error(`Failed to load messages for ${locale}`);
      }
      
      const messagesData = await response.json();
      this.messages[locale] = messagesData;
    } catch (error) {
      console.error(`Error loading messages for ${locale}:`, error);
      // If we failed to load the requested locale, and it's not the default, try to load the default
      if (locale !== this.defaultLocale) {
        await this.loadMessages(this.defaultLocale);
      }
    }
  }

  // Set the current locale
  public async setLocale(locale: string): Promise<void> {
    if (this.currentLocale === locale) return;
    
    this.currentLocale = locale;
    
    // Load messages for the new locale if not already loaded
    if (!this.messages[locale]) {
      await this.loadMessages(locale);
    }
    
    // Set document language attribute for proper language display
    if (document && document.documentElement) {
      document.documentElement.setAttribute('lang', locale);
    }
  }

  // Get a localized message
  public getMessage(key: string): string {
    // Try to get message from current locale
    if (this.messages[this.currentLocale] && this.messages[this.currentLocale][key]) {
      return this.messages[this.currentLocale][key].message;
    }
    
    // Try Chrome's built-in i18n API
    const chromeMessage = chrome.i18n.getMessage(key);
    if (chromeMessage) {
      return chromeMessage;
    }
    
    // Fall back to default locale
    if (this.currentLocale !== this.defaultLocale && 
        this.messages[this.defaultLocale] && 
        this.messages[this.defaultLocale][key]) {
      return this.messages[this.defaultLocale][key].message;
    }
    
    // Return key as fallback
    return key;
  }

  // Get current locale
  public getCurrentLocale(): string {
    return this.currentLocale;
  }
}

export default LocalizationManager;
