/**
 * Simple Markdown to HTML converter for displaying markdown in the popup
 */

/**
 * Convert markdown text to HTML
 * @param {string} markdown - The markdown text to convert
 * @returns {string} - The HTML representation of the markdown
 */
export const markdownToHtml = (markdown) => {
  if (!markdown || typeof markdown !== 'string') {
    return '';
  }

  let html = markdown;

  // Handle code blocks (```code```)
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

  // Handle inline code (`code`)
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Handle bold (**text**)
  html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

  // Handle italic (*text*)
  html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');

  // Handle headers (# Header)
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');

  // Handle unordered lists
  html = html.replace(/^\s*[\-\*] (.*$)/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

  // Handle ordered lists
  html = html.replace(/^\s*\d+\. (.*$)/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>');

  // Handle paragraphs and line breaks
  // Replace double newlines with paragraph breaks
  html = html.replace(/\n\n/g, '</p><p>');
  
  // Replace single newlines with line breaks
  html = html.replace(/\n/g, '<br>');

  // Wrap in paragraph tags if not already wrapped
  if (!html.startsWith('<')) {
    html = `<p>${html}</p>`;
  }

  return html;
};
