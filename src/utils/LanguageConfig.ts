// Language configuration mapping values to labels
export interface LanguageOption {
  value: string;
  label: string;
  apiValue: string; // The value to be sent to the API
}

export const LANGUAGE_OPTIONS: LanguageOption[] = [
  {
    value: 'en',
    label: 'English',
    apiValue: 'english'
  },
  {
    value: 'zh',
    label: '中文',
    apiValue: 'chinese'
  }
];

// Helper function to get the API value from a language value
export const getLanguageApiValue = (value: string): string => {
  const option = LANGUAGE_OPTIONS.find(opt => opt.value === value);
  return option ? option.apiValue : 'english'; // Default to english if not found
};

// Helper function to get language options for select components
export const getLanguageOptions = (): LanguageOption[] => {
  return LANGUAGE_OPTIONS;
};
